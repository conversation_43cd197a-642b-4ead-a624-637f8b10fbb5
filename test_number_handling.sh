#!/usr/bin/env bash

# 测试数字处理函数的脚本

# 数字处理函数：去除前导零，避免八进制解析错误
safe_number() {
  local num="$1"
  # 检查是否为有效数字
  if [[ "${num}" =~ ^[0-9]+$ ]]; then
    # 使用10#前缀强制十进制解析
    echo $((10#${num}))
  else
    echo "0"
    return 1
  fi
}

# 安全的数值比较函数
safe_compare() {
  local num1="$1"
  local num2="$2"
  local op="$3"  # 操作符：eq, lt, gt, le, ge, ne
  
  local safe_num1 safe_num2
  safe_num1=$(safe_number "$num1") || return 1
  safe_num2=$(safe_number "$num2") || return 1
  
  case "$op" in
    "eq") (( safe_num1 == safe_num2 ));;
    "lt") (( safe_num1 < safe_num2 ));;
    "gt") (( safe_num1 > safe_num2 ));;
    "le") (( safe_num1 <= safe_num2 ));;
    "ge") (( safe_num1 >= safe_num2 ));;
    "ne") (( safe_num1 != safe_num2 ));;
    *) return 1;;
  esac
}

echo "=== 测试数字处理函数 ==="

# 测试用例
test_numbers=("08" "09" "010" "001" "8" "9" "10" "1" "0" "00")

echo "测试 safe_number 函数："
for num in "${test_numbers[@]}"; do
  result=$(safe_number "$num")
  echo "safe_number('$num') = $result"
done

echo
echo "测试 safe_compare 函数："

# 测试比较操作
test_cases=(
  "08 8 eq"
  "09 9 eq" 
  "010 10 eq"
  "08 09 lt"
  "09 08 gt"
  "08 8 eq"
  "010 8 gt"
)

for case in "${test_cases[@]}"; do
  read -r num1 num2 op <<< "$case"
  if safe_compare "$num1" "$num2" "$op"; then
    result="TRUE"
  else
    result="FALSE"
  fi
  echo "safe_compare('$num1', '$num2', '$op') = $result"
done

echo
echo "=== 测试原始bash算术运算（会出错的情况） ==="

# 这些会导致错误
problematic_numbers=("08" "09")

for num in "${problematic_numbers[@]}"; do
  echo "测试原始算术运算: $num"
  
  # 这会导致错误
  set +e
  result=$((num + 0)) 2>/dev/null
  exit_code=$?
  set -e
  
  if [[ $exit_code -ne 0 ]]; then
    echo "  原始算术运算失败: $num (退出码: $exit_code)"
  else
    echo "  原始算术运算成功: $num = $result"
  fi
  
  # 使用安全函数
  safe_result=$(safe_number "$num")
  echo "  安全函数结果: $num = $safe_result"
  echo
done

echo "=== 测试完成 ==="
