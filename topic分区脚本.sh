#!/usr/bin/env bash

set -Eeuo pipefail

# ============ 配置区（可按需修改） ============
KAFKA_BIN="/home/<USER>/sds/kafka_client/kafka/bin"      # kafka-topics.sh 所在目录
TOPIC_LIST_FILE=""                                   # 外部topic列表文件（可选）
DRY_RUN="false"                                      # true=演练不执行变更
COLOR_LOG="true"                                     # 是否使用彩色日志
ASK_CONFIRMATION="true"                              # 执行前是否要求确认

# Kerberos/SASL（如未启用可设为 false）
KERBEROS_ENABLED="true"
JAAS_CONF="/home/<USER>/sds/RKDT/init/config/jaas.conf"  # JAAS 文件（KafkaClient 节）
KRB5_CONF="/home/<USER>/sds/RKDT/init/config/krb5.conf"  
ZK_SASL_SERVICE_NAME="zookeeper"                     # ZK 服务主体 primary（一般为 zookeeper）
# ===========================================

# 版本信息
VERSION="1.1.0"
SCRIPT_NAME="Kafka Topic Partition Adjuster"

# 彩色日志定义
if [[ "$COLOR_LOG" == "true" ]] && [[ -t 1 ]]; then
  RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[0;33m'; BLUE='\033[0;34m'; CYAN='\033[0;36m'; NC='\033[0m'
  BOLD='\033[1m'
else
  RED=''; GREEN=''; YELLOW=''; BLUE=''; CYAN=''; NC=''; BOLD=''
fi

# 全局变量
TOPICS_CLI=""
LOG_TS="$(date +%Y%m%d-%H%M%S)"
LOG_FILE="adjust_partitions_${LOG_TS}.log"
ZK_CONNECT=""

# 内置主题列表（作为备用）
read -r -d '' DEFAULT_TOPIC_PARTS <<'EOF'
bangcle_message_decrypt_512 2
bangcle_message_encrypt_512 2
wechat_mini_programme_topic_512 2
bangcle_message_intel_center_512 2
bangcle_message_receiverupload_512 2
bangcle_message_stat_512 2
bangcle_base_msg_512 2
bangcle_prop_info_512 2
bangcle_information_512 2
bangcle_stat_msg_512 2
bangcle_crash_stack_analysis_512 2
security_event_data_track_512 2
bangcle_aograph_msg_512 2
bangcle_mode_statistics_512 2
bangcle_dti_512 2
bangcle_model_msg_512 2
bangcle_backup_msg_512 2
bangcle_dev_fingerprint_512 2
bangcle_threat_index_512 2
bangcle_threat_index_query_512 2
bangcle_model_handle_msg_512 2
bangcle_cmbc_ban_record_512 2
bangcle_cmbc_data_push_512 2
bangcle_ban_record_to_cleaner_512 2
bangcle_ban_record_to_transfer_512 2
bangcle_receiver_flow_monitor_512 2
hxb_peoples_bank_512 2
bangcle_ccb_flow_control_record_512 2
ccb_push_data_output_512 2
bangcle_ceb_flow_monitor_512 2
security_event.history_recalculate_task_512 2
security_event.history_data_migration_512 2
security_event.history_recalculate_512 2
bangcle_data_push_512 2
bc_pingtai_relevance_devinfo_512 2
bangcle_new_dev_fingerprint_512 2
bangcle_dev_factor_512 2
bangcle_datapush_512 2
EOF

# 带颜色的日志函数
log() {
  local level="$1"; shift
  local message="$*"
  local timestamp=$(date '+%F %T')
  
  case "$level" in
    "ERROR") echo -e "${timestamp} ${RED}${BOLD}${level}${NC} ${message}" | tee -a "$LOG_FILE" >&2;;
    "WARN")  echo -e "${timestamp} ${YELLOW}${level}${NC} ${message}" | tee -a "$LOG_FILE";;
    "SUCCESS") echo -e "${timestamp} ${GREEN}${BOLD}${level}${NC} ${message}" | tee -a "$LOG_FILE";;
    "INFO")  echo -e "${timestamp} ${GREEN}${level}${NC} ${message}" | tee -a "$LOG_FILE";;
    "DEBUG") echo -e "${timestamp} ${BLUE}${level}${NC} ${message}" | tee -a "$LOG_FILE";;
    "STEP")  echo -e "${timestamp} ${CYAN}${BOLD}${level}${NC} ${message}" | tee -a "$LOG_FILE";;
    *)       echo -e "${timestamp} ${message}" | tee -a "$LOG_FILE";;
  esac
}

# 显示用法信息
usage() {
  echo -e "${GREEN}${BOLD}${SCRIPT_NAME} v${VERSION}${NC}"
  echo -e "${GREEN}用法: $0 --zk-connect <zk连接字符串> [选项]${NC}"
  echo
  echo -e "${BOLD}选项:${NC}"
  echo -e "  -z, --zk-connect STRING    ZooKeeper连接字符串 (必需)"
  echo -e "      --dry-run              演练模式，不执行实际变更"
  echo -e "  -f, --file FILE           指定外部topic列表文件"
  echo -e "      --no-color            禁用彩色输出"
  echo -e "      --no-confirm          跳过确认提示"
  echo -e "  -h, --help                显示此帮助信息"
  echo
  echo -e "${BOLD}示例:${NC}"
  echo -e "  ${CYAN}$0 --zk-connect 'zk1:2181,zk2:2181/kafka-prod' --dry-run${NC}"
  echo -e "  ${CYAN}$0 --zk-connect 'zk1:2181' -f topic_list.txt${NC}"
  exit 1
}

# 检查依赖项
check_dependencies() {
  local missing_deps=()
  
  # 检查kafka-topics.sh
  if [[ -d "${KAFKA_BIN}" ]]; then
    TOPICS_CLI="${KAFKA_BIN}/kafka-topics.sh"
    if [[ ! -x "${TOPICS_CLI}" ]]; then
      log "ERROR" "kafka-topics.sh 不可执行: ${TOPICS_CLI}"
      missing_deps+=("kafka-topics.sh")
    fi
  else
    log "ERROR" "Kafka目录不存在: ${KAFKA_BIN}"
    missing_deps+=("kafka-topics.sh")
  fi
  
  # 如果有缺失的依赖项，退出
  if [[ ${#missing_deps[@]} -gt 0 ]]; then
    log "ERROR" "缺少必要的依赖项: ${missing_deps[*]}"
    exit 1
  fi
  
  log "DEBUG" "使用Kafka工具: ${TOPICS_CLI}"
}

# 验证ZK连接字符串格式
validate_zk_connect() {
  local zk="$1"
  # 基本格式验证: host:port[,host:port...][/chroot]
  if ! [[ "$zk" =~ ^[a-zA-Z0-9._-]+(:[0-9]+)+(,[a-zA-Z0-9._-]+(:[0-9]+)+)*(\/[a-zA-Z0-9._-]+)?$ ]]; then
    log "ERROR" "无效的ZK连接格式: $zk (示例: zk1:2181 或 zk1:2181,zk2:2181/kafka)"
    return 1
  fi
  return 0
}

# 检查主题是否存在
topic_exists() {
  local t="$1"
  log "DEBUG" "检查主题是否存在: ${t}"
  "${TOPICS_CLI}" --describe --zookeeper "${ZK_CONNECT}" --topic "${t}" >> "$LOG_FILE" 2>&1
}

# 获取当前分区数
get_current_partitions() {
  local t="$1"
  log "DEBUG" "获取当前分区数: ${t}"
  "${TOPICS_CLI}" --describe --zookeeper "${ZK_CONNECT}" --topic "${t}" 2>> "$LOG_FILE" \
    | awk -F'[ :\t]+' '/^Topic:/{ for(i=1;i<=NF;i++){ if($i=="PartitionCount"){ print $(i+1); exit } } }'
}

# 修改分区数（完整输出）
alter_partitions() {
  local t="$1" new_count="$2"
  log "INFO" "执行分区调整: ${t} -> ${new_count}"
  echo "===========================================" >> "$LOG_FILE"
  local output
  output=$("${TOPICS_CLI}" --alter --topic "${t}" --partitions "${new_count}" --zookeeper "${ZK_CONNECT}" 2>&1)
  local exit_code=$?
  echo "$output" >> "$LOG_FILE"
  echo "===========================================" >> "$LOG_FILE"
  
  # 同时显示在控制台
  if [[ -n "$output" ]]; then
    echo "$output"
  fi
  
  return $exit_code
}

# 打印主题详细信息
print_topic_summary() {
  local t="$1"
  log "DEBUG" "主题详细信息: ${t}"
  echo "--------- 主题 ${t} 详细信息 ---------" | tee -a "$LOG_FILE"
  "${TOPICS_CLI}" --describe --zookeeper "${ZK_CONNECT}" --topic "${t}" | tee -a "$LOG_FILE"
  echo "-------------------------------------" | tee -a "$LOG_FILE"
}

# 测试ZK连接
test_zk_connection() {
  log "STEP" "测试ZooKeeper连接..."
  
  # 尝试列出topics来测试连接
  if "${TOPICS_CLI}" --list --zookeeper "${ZK_CONNECT}" >> "$LOG_FILE" 2>&1; then
    log "SUCCESS" "ZooKeeper连接测试成功"
    return 0
  else
    log "ERROR" "ZooKeeper连接测试失败，请检查连接字符串和网络连通性"
    return 1
  fi
}

# 确认操作
confirm_operation() {
  local total_changes=$(( CNT_TOTAL - CNT_SKIPPED_EQ - CNT_SKIPPED_DECREASE - CNT_NOT_FOUND ))
  
  if [[ $total_changes -eq 0 ]]; then
    log "INFO" "没有需要变更的分区，无需确认"
    return 0
  fi
  
  log "WARN" "即将修改 ${BOLD}${total_changes}${NC} 个主题的分区数"
  log "WARN" "详细变更列表请查看日志: ${LOG_FILE}"
  
  if [[ "$DRY_RUN" == "true" ]]; then
    log "INFO" "DRY-RUN 模式，不会实际执行变更"
    return 0
  fi

  if [[ "$ASK_CONFIRMATION" == "true" ]]; then
    echo
    read -p "是否确认执行? (y/N): " choice
    case "$choice" in
      y|Y) log "INFO" "确认执行"; return 0;;
      *)   log "INFO" "用户取消操作"; exit 0;;
    esac
  else
    log "INFO" "跳过确认提示，直接执行"
    return 0
  fi
}

# 解析主题列表
parse_topic_list() {
  local topic_list_content="$1"
  local source="$2"
  
  log "STEP" "从${source}解析主题列表..."
  
  while IFS= read -r line; do
    # 移除前后空白和注释行
    line="${line#"${line%%[![:space:]]*}"}"
    line="${line%"${line##*[![:space:]]}"}"
    [[ -z "$line" || "${line:0:1}" == "#" ]] && continue
    
    # 解析主题名和目标分区数
    topic="$(awk '{print $1}' <<<"$line")"
    desired="$(awk '{print $2}' <<<"$line")"
    
    # 验证格式
    if [[ -z "${topic}" || -z "${desired}" ]]; then
      log "WARN" "行格式不正确(缺少字段)，忽略：$line"
      continue
    fi
    
    if ! [[ "${desired}" =~ ^[0-9]+$ ]]; then
      log "WARN" "分区数必须为数字，忽略：$line"
      continue
    fi
    
    if (( desired < 1 )); then
      log "WARN" "分区数必须大于0，忽略：$line"
      continue
    fi
    
    # 去重处理
    if [[ -n "${SEEN_TOPICS[$topic]:-}" ]]; then
      log "DEBUG" "跳过重复主题: ${topic}"
      continue
    fi
    
    SEEN_TOPICS["$topic"]="${desired}"
    CNT_TOTAL+=1
    log "DEBUG" "添加主题: ${topic} -> ${desired}"
  done <<< "${topic_list_content}"
}

# 主函数
main() {
  log "STEP" "${SCRIPT_NAME} v${VERSION} 开始执行"
  log "INFO" "参数: ZK=${ZK_CONNECT}, DRY_RUN=${DRY_RUN}, TOPIC_FILE=${TOPIC_LIST_FILE}"
  log "INFO" "日志文件: ${LOG_FILE}"

  # 检查依赖
  check_dependencies

  # 验证ZK连接格式
  if ! validate_zk_connect "${ZK_CONNECT}"; then
    exit 1
  fi

  # 测试ZK连接
  if ! test_zk_connection; then
    exit 1
  fi

  # Kerberos 配置检查
  if [[ "${KERBEROS_ENABLED}" == "true" ]]; then
    if [[ ! -f "${JAAS_CONF}" ]]; then
      log "ERROR" "JAAS 配置文件不存在: ${JAAS_CONF}"
      exit 1
    fi
    if [[ ! -f "${KRB5_CONF}" ]]; then
      log "ERROR" "KRB5 配置文件不存在: ${KRB5_CONF}"
      exit 1
    fi
    export KAFKA_OPTS="-Djava.security.auth.login.config=${JAAS_CONF} -Djava.security.krb5.conf=${KRB5_CONF} -Dzookeeper.sasl.client=true -Dzookeeper.sasl.client.username=${ZK_SASL_SERVICE_NAME}"
    log "DEBUG" "启用Kerberos认证"
  else
    export KAFKA_OPTS=""
    log "DEBUG" "未启用Kerberos认证"
  fi

  # 初始化统计变量
  declare -i CNT_TOTAL=0 CNT_SKIPPED_EQ=0 CNT_SKIPPED_DECREASE=0 CNT_UPDATED=0 CNT_FAILED=0 CNT_NOT_FOUND=0
  declare -A SEEN_TOPICS=()  # 用于去重

  # 解析主题列表
  if [[ -n "${TOPIC_LIST_FILE}" && -f "${TOPIC_LIST_FILE}" ]]; then
    parse_topic_list "$(cat "${TOPIC_LIST_FILE}")" "文件 ${TOPIC_LIST_FILE}"
  else
    if [[ -n "${TOPIC_LIST_FILE}" ]]; then
      log "WARN" "主题列表文件不存在: ${TOPIC_LIST_FILE}，使用内置列表"
    fi
    parse_topic_list "${DEFAULT_TOPIC_PARTS}" "内置列表"
  fi

  log "INFO" "解析完成: 总计 ${CNT_TOTAL} 个主题，去重后 ${#SEEN_TOPICS[@]} 个唯一主题"

  # 如果没有找到任何主题
  if [[ ${#SEEN_TOPICS[@]} -eq 0 ]]; then
    log "ERROR" "未找到任何有效的主题配置"
    exit 1
  fi

  # 执行前确认
  confirm_operation

  # 处理每个主题
  for topic in "${!SEEN_TOPICS[@]}"; do
    desired_count="${SEEN_TOPICS[$topic]}"
    log "INFO" "处理主题: ${topic} => 目标分区 ${desired_count}"

    if ! topic_exists "${topic}"; then
      log "WARN" "主题不存在，跳过：${topic}"
      CNT_NOT_FOUND+=1
      continue
    fi

    current_count="$(get_current_partitions "${topic}")"
    if [[ -z "${current_count}" ]]; then
      log "ERROR" "无法获取当前分区数：${topic}"
      CNT_FAILED+=1
      continue
    fi
    log "INFO" "当前分区数: ${current_count}"

    if (( desired_count == current_count )); then
      log "INFO" "跳过：目标与当前分区数一致"
      CNT_SKIPPED_EQ+=1
      continue
    fi

    if (( desired_count < current_count )); then
      log "WARN" "跳过：Kafka不支持减少分区（${current_count} -> ${desired_count}）"
      CNT_SKIPPED_DECREASE+=1
      continue
    fi

    if [[ "${DRY_RUN}" == "true" ]]; then
      log "INFO" "DRY-RUN: 将执行 ${topic} ${current_count} -> ${desired_count}"
      print_topic_summary "${topic}"
      continue
    fi

    if alter_partitions "${topic}" "${desired_count}"; then
      # 等待一下让变更生效
      sleep 1
      after_count="$(get_current_partitions "${topic}")"
      if [[ "${after_count}" == "${desired_count}" ]]; then
        log "SUCCESS" "成功：${topic} 已更新为 ${after_count}"
        CNT_UPDATED+=1
        print_topic_summary "${topic}"
      else
        log "ERROR" "校验失败（期望=${desired_count} 实际=${after_count}）：${topic}"
        CNT_FAILED+=1
        print_topic_summary "${topic}"
      fi
    else
      log "ERROR" "执行分区调整失败：${topic}"
      CNT_FAILED+=1
    fi
  done

  # 输出最终统计
  echo
  log "STEP" "===== 执行结果统计 ====="
  log "INFO" "解析主题总数: ${CNT_TOTAL}"
  log "INFO" "去重后实际处理: ${#SEEN_TOPICS[@]}"
  log "SUCCESS" "成功变更: ${CNT_UPDATED}"
  log "INFO" "跳过（分区数已满足）: ${CNT_SKIPPED_EQ}"
  log "INFO" "跳过（不支持减少分区）: ${CNT_SKIPPED_DECREASE}"
  log "WARN" "跳过（主题不存在）: ${CNT_NOT_FOUND}"
  if [[ ${CNT_FAILED} -gt 0 ]]; then
    log "ERROR" "失败操作: ${CNT_FAILED}"
  else
    log "INFO" "失败操作: ${CNT_FAILED}"
  fi
  log "INFO" "详细日志请查看: ${LOG_FILE}"
}

# 解析命令行参数
parse_arguments() {
  while [[ $# -gt 0 ]]; do
    case "$1" in
      -z|--zk|--zookeeper|--zk-connect)
        shift
        ZK_CONNECT="${1:-}"
        [[ -z "$ZK_CONNECT" ]] && {
          log "ERROR" "ZK连接字符串不能为空"
          usage
        }
        ;;
      --dry-run)
        DRY_RUN="true"
        ;;
      -f|--file)
        shift
        TOPIC_LIST_FILE="${1:-}"
        ;;
      --no-color)
        COLOR_LOG="false"
        ;;
      --no-confirm)
        ASK_CONFIRMATION="false"
        ;;
      -h|--help)
        usage
        ;;
      *)
        log "ERROR" "未知参数: $1"
        usage
        ;;
    esac
    shift || true
  done

  # 必需参数检查
  [[ -z "${ZK_CONNECT}" ]] && {
    log "ERROR" "必须提供ZK连接字符串"
    usage
  }
}

# 脚本入口
parse_arguments "$@"
main
