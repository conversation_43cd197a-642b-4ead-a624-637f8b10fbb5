#!/usr/bin/env bash

# 简单测试脚本，验证修复是否有效

echo "=== 测试脚本基本功能 ==="

# 测试 read -r -d '' 是否正常工作
echo "测试 read -r -d '' 命令..."

# 这应该不会导致脚本退出
read -r -d '' TEST_DATA <<'EOF' || true
test_topic_1 2
test_topic_2 4
test_topic_3 8
EOF

echo "read 命令执行成功，内容长度: ${#TEST_DATA}"

# 测试数字处理
echo "测试数字处理..."

# 模拟有问题的数字
test_numbers=("08" "09" "010" "001")

for num in "${test_numbers[@]}"; do
  # 使用 10# 前缀强制十进制解析
  result=$((10#${num}))
  echo "10#${num} = ${result}"
done

echo "=== 测试完成 ==="
